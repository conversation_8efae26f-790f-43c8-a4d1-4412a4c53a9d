# ElectronicController_Board

本仓库包含米醋电子工作室开发的电子控制器板相关代码。

## Web 上位机

为了方便调试和控制 PID 控制器 (`App/app_pid.c`)，本项目提供了一个基于 Web 的上位机界面。

### 功能

*   **串口连接**: 自动检测可用串口，支持设置波特率并连接/断开设备。
*   **状态显示**: 实时显示 X/Y 轴的目标位置、实际位置和电机输出速度。
*   **位置曲线**: 在同一个图表中实时绘制 X 轴目标/实际值 和 Y 轴目标/实际值 随时间变化的曲线。
*   **PID 曲线**: （需设备端开启调试）实时绘制 X/Y 轴 PID 控制器的 P, I, D 分量输出曲线。
*   **图表交互**: 所有曲线图表的 Y 轴支持通过鼠标滚轮或触摸板手势进行缩放，通过鼠标拖拽进行平移。
*   **PID 控制**: 发送启动和停止 PID 控制的命令。
*   **目标设置**: 通过输入框设置 X/Y 轴的目标坐标。
*   **参数调节**: 通过滑动条实时调整 X/Y 轴的 PID 参数 (Kp, Ki, Kd)。
*   **日志输出**: 显示从设备接收到的原始数据和上位机操作日志。
*   **数据保留**: 图表数据（最近 100 个点）会自动保存在浏览器本地存储中，刷新页面或下次访问时会尝试恢复。

### 使用方法

1.  **安装依赖**: 确保您的计算机已安装 Python 3 和 pip。
2.  **进入目录**: 在终端或命令行中，导航到 `Web` 目录。
3.  **运行启动脚本**:
    *   **Windows**: 双击运行 `start.bat`。
    *   **Linux/macOS**: 在终端中运行 `chmod +x start.sh` (首次运行时需要赋予执行权限)，然后运行 `./start.sh`。
4.  **启动服务**: 脚本会自动安装所需的 Python 库并启动 Web 服务器。
5.  **访问上位机**: 脚本启动后，会提示服务器地址 (通常是 `http://127.0.0.1:5000` 或 `http://0.0.0.0:5000`)。在您的网页浏览器中打开此地址。
6.  **连接设备**: 在网页界面中选择您的设备连接的串口号，确认波特率（默认为 115200，与 `app_pid.c` 中使用的 `my_printf` 相关串口配置应一致），然后点击"连接"按钮。
7.  **操作**: 连接成功后，即可使用界面上的各种功能进行监控和控制。

### 摄像头数据格式

系统支持MaixCam摄像头数据解析，用于激光追踪功能：

*   **红色激光数据格式**: `R,x,y` - 表示检测到的红色激光点坐标，用作当前实际位置
*   **目标位置**: 系统使用固定目标坐标 `x=150, y=95`
*   **数据处理**: 通过UART3接收摄像头数据，使用环形缓冲区进行数据管理
*   **PID控制**: 接收到红色激光坐标后，自动更新当前位置并设置固定目标，启动PID追踪控制

### 注意事项

*   图表数据保留功能依赖浏览器本地存储（LocalStorage）。如果浏览器禁用或清除本地存储，数据将丢失。
*   要查看 PID 实时曲线，需要确保设备端代码 (`App/app_pid.c` 或 `App/app_pid.h`) 中定义了 `PID_DEBUG_ENABLE` 为 1，并重新编译烧录固件。
*   确保设备通过 `huart1` (根据 `App/app_pid.h` 中的定义) 连接到计算机，并在上位机界面中选择了正确的 COM 端口。
*   确保设备固件中的波特率与上位机设置的波特率一致。
*   上位机与设备之间的通信协议细节基于 `App/app_pid.c` 中的 `app_pid_report` 和 `app_pid_parse_cmd` 函数实现。如果修改了设备端的协议，需要同步更新 `Web/server.py` 中的解析和命令格式代码。
*   摄像头数据解析功能在 `App/app_maixcam.c` 中实现，支持 `R,x,y` 格式的红色激光坐标数据。

---
Copyright (c) 2024 米醋电子工作室. 保留所有权利。 